#!/usr/bin/env python3
"""
Script to generate Alembic migrations following the project naming convention.
Usage: python scripts/generate-migration.py "Description of the migration"
"""

import os
import sys
import re
from datetime import datetime
from pathlib import Path

def get_next_migration_number():
    """Get the next sequential migration number"""
    migrations_dir = Path("migrations/versions")
    if not migrations_dir.exists():
        return "0001"
    
    # Find all migration files
    migration_files = list(migrations_dir.glob("*.py"))
    if not migration_files:
        return "0001"
    
    # Extract numbers from existing migrations
    numbers = []
    for file in migration_files:
        match = re.match(r"(\d{4})_", file.name)
        if match:
            numbers.append(int(match.group(1)))
    
    if not numbers:
        return "0001"
    
    return f"{max(numbers) + 1:04d}"

def get_last_revision():
    """Get the revision ID of the last migration"""
    migrations_dir = Path("migrations/versions")
    if not migrations_dir.exists():
        return None
    
    migration_files = list(migrations_dir.glob("*.py"))
    if not migration_files:
        return None
    
    # Sort by migration number
    migration_files.sort(key=lambda x: x.name)
    
    # Read the last migration file to get its revision ID
    last_file = migration_files[-1]
    with open(last_file, 'r') as f:
        content = f.read()
        match = re.search(r"revision = ['\"]([^'\"]+)['\"]", content)
        if match:
            return match.group(1)
    
    return None

def create_migration(description):
    """Create a new migration file"""
    # Generate migration details
    migration_number = get_next_migration_number()
    now = datetime.now()
    date_str = now.strftime("%Y%m%d")
    time_str = now.strftime("%H%M")
    
    # Clean description for filename
    clean_desc = re.sub(r'[^a-zA-Z0-9_]', '_', description.lower())
    clean_desc = re.sub(r'_+', '_', clean_desc).strip('_')
    
    # Generate revision ID
    revision_id = f"{migration_number}_{date_str}_{time_str}"
    
    # Get previous revision
    down_revision = get_last_revision()
    
    # Create filename
    filename = f"{migration_number}_{date_str}_{time_str}_{clean_desc}.py"
    filepath = Path("migrations/versions") / filename
    
    # Create migration content
    content = f'''"""Add {description}

Revision ID: {revision_id}
Revises: {down_revision or 'None'}
Create Date: {now.strftime("%Y-%m-%d %H:%M:%S.%f")}

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = {repr(down_revision)}
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
'''
    
    # Write the file
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"Migration created: {filename}")
    print(f"Revision ID: {revision_id}")
    print(f"Edit the file to add your migration logic.")
    
    return filepath

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python scripts/generate-migration.py \"Description of the migration\"")
        sys.exit(1)
    
    description = sys.argv[1]
    create_migration(description)
