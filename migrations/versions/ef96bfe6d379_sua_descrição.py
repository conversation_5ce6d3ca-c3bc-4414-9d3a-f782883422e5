"""Sua descrição

Revision ID: ef96bfe6d379
Revises: 0001
Create Date: 2025-08-07 16:54:57.217235

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ef96bfe6d379'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('media_conversions', 'media_type',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.Enum('VIDEO', 'IMAGE', 'AUDIO', name='media_type'),
               existing_nullable=False)
    op.alter_column('media_conversions', 'status',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversion_status'),
               existing_nullable=False)
    op.alter_column('media_conversions', 'output_format',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.Enum('DASH', 'HLS', 'WEBP', 'AVIF', 'JPEG', 'PNG', 'MP3', 'AAC', 'OGG', name='output_format'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('media_conversions', 'output_format',
               existing_type=sa.Enum('DASH', 'HLS', 'WEBP', 'AVIF', 'JPEG', 'PNG', 'MP3', 'AAC', 'OGG', name='output_format'),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('media_conversions', 'status',
               existing_type=sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversion_status'),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('media_conversions', 'media_type',
               existing_type=sa.Enum('VIDEO', 'IMAGE', 'AUDIO', name='media_type'),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    # ### end Alembic commands ###
