"""Add enum constraints for media_type, status, and output_format

Revision ID: 0002_20250107
Revises: 0001_20250723_2300
Create Date: 2025-01-07 12:00:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0002_20250107'
down_revision = '0001_20250723_2300'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add enum constraints to media_type, status, and output_format columns"""
    
    # Create ENUM types
    media_type_enum = postgresql.ENUM('video', 'image', 'audio', name='media_type')
    media_type_enum.create(op.get_bind())

    status_enum = postgresql.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversion_status')
    status_enum.create(op.get_bind())

    output_format_enum = postgresql.ENUM(
        'dash', 'hls', 'webp', 'avif', 'jpeg', 'png', 'mp3', 'aac', 'ogg',
        name='output_format'
    )
    output_format_enum.create(op.get_bind())
    
    # Alter columns to use ENUM types
    op.alter_column('media_conversions', 'media_type',
                    existing_type=sa.VARCHAR(length=50),
                    type_=media_type_enum,
                    existing_nullable=False,
                    postgresql_using='media_type::media_type')

    op.alter_column('media_conversions', 'status',
                    existing_type=sa.VARCHAR(length=50),
                    type_=status_enum,
                    existing_nullable=False,
                    existing_server_default=sa.text("'PENDING'"),
                    postgresql_using='status::conversion_status')

    op.alter_column('media_conversions', 'output_format',
                    existing_type=sa.VARCHAR(length=50),
                    type_=output_format_enum,
                    existing_nullable=True,
                    postgresql_using='output_format::output_format')


def downgrade() -> None:
    """Remove enum constraints and revert to VARCHAR"""

    # Revert columns to VARCHAR
    op.alter_column('media_conversions', 'output_format',
                    existing_type=postgresql.ENUM('dash', 'hls', 'webp', 'avif', 'jpeg', 'png', 'mp3', 'aac', 'ogg', name='output_format'),
                    type_=sa.VARCHAR(length=50),
                    existing_nullable=True)

    op.alter_column('media_conversions', 'status',
                    existing_type=postgresql.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversion_status'),
                    type_=sa.VARCHAR(length=50),
                    existing_nullable=False,
                    existing_server_default=sa.text("'PENDING'"))

    op.alter_column('media_conversions', 'media_type',
                    existing_type=postgresql.ENUM('video', 'image', 'audio', name='media_type'),
                    type_=sa.VARCHAR(length=50),
                    existing_nullable=False)

    # Drop ENUM types
    postgresql.ENUM(name='output_format').drop(op.get_bind())
    postgresql.ENUM(name='conversion_status').drop(op.get_bind())
    postgresql.ENUM(name='media_type').drop(op.get_bind())
