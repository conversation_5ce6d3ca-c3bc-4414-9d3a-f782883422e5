# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.env.local
.env.development
.env.test
.env.production
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Celery
celerybeat-schedule
celerybeat.pid

# IDEs
.idea/
.vscode/
*.swp
*.swo
*~

# Tools
.ruff_cache/
.mypy_cache/
.dmypy.json
dmypy.json

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Media Convert specific
temp/
tmp/
output/
dash_output/
test_output/

# Media files
*.mp4
*.avi
*.mov
*.mkv
*.webm
*.m4v
*.mpd
*.m4s
*.jpg
*.jpeg
*.png
*.gif

# Database
*.db
*.sqlite
*.sqlite3


# Backup files
*.bak
*.backup

# Secrets
secrets/
credentials/
*.pem
*.key
*.crt

# Github ACT
.secrets

# AWS
.aws/

# Local development
local_*
dev_*
.python-version

# SonarQube
.scannerwork/
.reports/
