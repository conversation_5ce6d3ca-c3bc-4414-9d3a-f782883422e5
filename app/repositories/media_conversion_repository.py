"""Media Conversion Repository for database operations"""

from datetime import datetime
from typing import Any

from sqlalchemy.exc import SQLAlchemyError

from app.database.connection import get_db_session, get_db_session_readonly
from app.database.models import (
    ConversionStatus,
    MediaConversion,
    MediaType,
    OutputFormat,
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


class MediaConversionRepository:
    """Repository for managing media conversion records in the database"""

    @staticmethod
    def create_conversion(
        conversion_id: str,
        input_path: str,
        media_type: MediaType,
        output_format: OutputFormat | None = None,
        metadata: dict[str, Any] | None = None,
    ) -> MediaConversion | None:
        """
        Create a new media conversion record

        Args:
            conversion_id: Unique conversion identifier from message payload
            input_path: Path to the input media file
            media_type: Type of media (video, image, audio)
            output_format: Target output format (dash, webp, etc.)
            metadata: Optional metadata dictionary

        Returns:
            MediaConversion object or None if failed
        """
        try:
            with get_db_session() as session:
                conversion = MediaConversion(
                    id=conversion_id,
                    input_path=input_path,
                    media_type=media_type,
                    output_format=output_format,
                    status=ConversionStatus.PENDING,
                    metadata_json=metadata or {},
                )

                session.add(conversion)
                session.flush()  # Force ID generation without committing

                logger.info(
                    f"Created {media_type.value} conversion record: {conversion.id}",
                )
                return conversion

        except SQLAlchemyError as e:
            # Check if it's a duplicate key error
            error_str = str(e).lower()
            if "duplicate key" in error_str or "unique constraint" in error_str:
                logger.warning(
                    f"Conversion {conversion_id} already exists - "
                    "duplicate request ignored",
                )
                return None
            logger.error(
                f"Failed to create conversion record for {conversion_id}: {e}",
            )
            return None

    @staticmethod
    def update_status(
        conversion_id: str,
        status: ConversionStatus | str,
        output_path: str | None = None,
        processing_duration_seconds: int | None = None,
        error_message: str | None = None,
        metadata: dict[str, Any] | None = None,
    ) -> bool:
        """
        Update conversion status and details

        Args:
            conversion_id: Conversion identifier
            status: New status (PENDING, PROCESSING, COMPLETED, FAILED)
            output_path: Path to output file (for COMPLETED status)
            processing_duration_seconds: Processing duration in seconds
            error_message: Error details (for FAILED status)
            metadata: Additional metadata to merge

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            with get_db_session() as session:
                conversion = (
                    session.query(MediaConversion).filter_by(id=conversion_id).first()
                )

                if not conversion:
                    logger.error(f"Conversion record not found: {conversion_id}")
                    return False

                # Update fields
                conversion.status = status.value if isinstance(status, ConversionStatus) else status
                conversion.updated_at = datetime.now(datetime.timezone.utc)

                if output_path:
                    conversion.output_path = output_path

                if processing_duration_seconds is not None:
                    conversion.processing_duration_seconds = processing_duration_seconds

                if error_message:
                    conversion.error_message = error_message

                # Merge metadata
                if metadata:
                    current_metadata = conversion.metadata_json or {}
                    current_metadata.update(metadata)
                    conversion.metadata_json = current_metadata

                logger.info(f"Updated conversion {conversion.id} to status: {status}")
                return True

        except SQLAlchemyError as e:
            logger.error(f"Failed to update conversion {conversion_id}: {e}")
            return False

    @staticmethod
    def get_conversion(conversion_id: str) -> MediaConversion | None:
        """
        Get conversion record by conversion ID

        Args:
            conversion_id: Conversion identifier

        Returns:
            MediaConversion object or None if not found
        """
        try:
            with get_db_session_readonly() as session:
                conversion = (
                    session.query(MediaConversion).filter_by(id=conversion_id).first()
                )

                if conversion:
                    # Detach from session to avoid lazy loading issues
                    session.expunge(conversion)

                return conversion

        except SQLAlchemyError as e:
            logger.error(f"Failed to get conversion {conversion_id}: {e}")
            return None

    @staticmethod
    def list_conversions(
        status: str | None = None,
        media_type: MediaType | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[MediaConversion]:
        """
        List conversion records with optional filtering

        Args:
            status: Filter by status (optional)
            media_type: Filter by media type (optional)
            limit: Maximum number of records
            offset: Number of records to skip

        Returns:
            List of MediaConversion objects
        """
        try:
            with get_db_session_readonly() as session:
                query = session.query(MediaConversion)

                if status:
                    query = query.filter_by(status=status)

                if media_type:
                    query = query.filter_by(media_type=media_type.value)

                conversions = (
                    query.order_by(MediaConversion.created_at.desc())
                    .offset(offset)
                    .limit(limit)
                    .all()
                )

                # Detach from session
                for conversion in conversions:
                    session.expunge(conversion)

                return conversions

        except SQLAlchemyError as e:
            logger.error(f"Failed to list conversions: {e}")
            return []
