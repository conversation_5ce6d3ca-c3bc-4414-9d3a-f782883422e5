"""create_media_conversions_table

Revision ID: 2246481ba943
Revises: 
Create Date: 2025-08-07 17:09:23.687278

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2246481ba943'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('media_conversions',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('media_type', sa.Enum('VIDEO', 'IMAGE', 'AUDIO', name='media_type'), nullable=False),
    sa.Column('input_path', sa.String(length=1000), nullable=False),
    sa.Column('output_path', sa.String(length=1000), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversion_status'), nullable=False),
    sa.Column('output_format', sa.Enum('DASH', 'HLS', 'WEBP', 'AVIF', 'JPEG', 'PNG', 'MP3', 'AAC', 'OGG', name='output_format'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('processing_duration_seconds', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('metadata_json', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_conversions_media_type'), 'media_conversions', ['media_type'], unique=False)
    op.create_index(op.f('ix_media_conversions_output_format'), 'media_conversions', ['output_format'], unique=False)
    op.create_index(op.f('ix_media_conversions_status'), 'media_conversions', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_media_conversions_status'), table_name='media_conversions')
    op.drop_index(op.f('ix_media_conversions_output_format'), table_name='media_conversions')
    op.drop_index(op.f('ix_media_conversions_media_type'), table_name='media_conversions')
    op.drop_table('media_conversions')
    # ### end Alembic commands ###
